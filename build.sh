#!/bin/bash

# Worklog Synchronizer Build Script

echo "🔨 Building Worklog Synchronizer Plugin..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ Failed to install dependencies"
        exit 1
    fi
fi

# Run TypeScript compilation check
echo "🔍 Checking TypeScript..."
npx tsc --noEmit --skipLibCheck
if [ $? -ne 0 ]; then
    echo "❌ TypeScript compilation failed"
    exit 1
fi

# Build the plugin
echo "🏗️ Building plugin..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

# Check if required files exist
if [ ! -f "main.js" ]; then
    echo "❌ main.js not found after build"
    exit 1
fi

if [ ! -f "manifest.json" ]; then
    echo "❌ manifest.json not found"
    exit 1
fi

if [ ! -f "styles.css" ]; then
    echo "❌ styles.css not found"
    exit 1
fi

echo "✅ Build completed successfully!"
echo ""
echo "📁 Plugin files ready:"
echo "   - main.js"
echo "   - manifest.json" 
echo "   - styles.css"
echo ""
echo "📋 To install manually:"
echo "   1. Copy main.js, manifest.json, and styles.css"
echo "   2. Place them in: VaultFolder/.obsidian/plugins/worklog-synchronizer/"
echo "   3. Restart Obsidian"
echo "   4. Enable the plugin in Settings → Community Plugins"
echo ""
echo "🎉 Happy note-taking!"
