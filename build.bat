@echo off
REM Worklog Synchronizer Build Script for Windows

echo 🔨 Building Worklog Synchronizer Plugin...

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Run TypeScript compilation check
echo 🔍 Checking TypeScript...
npx tsc --noEmit --skipLibCheck
if %errorlevel% neq 0 (
    echo ❌ TypeScript compilation failed
    pause
    exit /b 1
)

REM Build the plugin
echo 🏗️ Building plugin...
npm run build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "main.js" (
    echo ❌ main.js not found after build
    pause
    exit /b 1
)

if not exist "manifest.json" (
    echo ❌ manifest.json not found
    pause
    exit /b 1
)

if not exist "styles.css" (
    echo ❌ styles.css not found
    pause
    exit /b 1
)

echo ✅ Build completed successfully!
echo.
echo 📁 Plugin files ready:
echo    - main.js
echo    - manifest.json
echo    - styles.css
echo.
echo 📋 To install manually:
echo    1. Copy main.js, manifest.json, and styles.css
echo    2. Place them in: VaultFolder\.obsidian\plugins\worklog-synchronizer\
echo    3. Restart Obsidian
echo    4. Enable the plugin in Settings → Community Plugins
echo.
echo 🎉 Happy note-taking!
pause
