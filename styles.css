/* Worklog Synchronizer Plugin Styles */

.sync-preview-summary {
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--background-secondary);
    border-radius: 8px;
}

.sync-preview-summary p {
    margin: 5px 0;
    font-weight: 500;
}

.sync-preview-errors {
    margin-top: 15px;
    padding: 10px;
    background-color: var(--background-modifier-error);
    border-radius: 5px;
    border-left: 4px solid var(--text-error);
}

.sync-preview-errors h3 {
    margin: 0 0 10px 0;
    color: var(--text-error);
}

.error-text {
    color: var(--text-error);
    font-family: var(--font-monospace);
    font-size: 0.9em;
}

.sync-preview-details {
    margin: 20px 0;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid var(--background-modifier-border);
    border-radius: 5px;
    padding: 10px;
}

.sync-preview-details h3 {
    margin: 0 0 15px 0;
    color: var(--text-normal);
}

.sync-detail {
    margin-bottom: 8px;
    padding: 8px;
    background-color: var(--background-primary-alt);
    border-radius: 4px;
    border-left: 3px solid var(--interactive-accent);
}

.sync-detail p {
    margin: 0;
    font-family: var(--font-monospace);
    font-size: 0.85em;
    line-height: 1.4;
}

.sync-preview-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--background-modifier-border);
}

.sync-preview-buttons button {
    padding: 8px 16px;
    border-radius: 4px;
    border: 1px solid var(--background-modifier-border);
    background-color: var(--background-secondary);
    color: var(--text-normal);
    cursor: pointer;
    transition: all 0.2s ease;
}

.sync-preview-buttons button:hover {
    background-color: var(--background-modifier-hover);
}

.sync-preview-buttons button.mod-cta {
    background-color: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
}

.sync-preview-buttons button.mod-cta:hover {
    background-color: var(--interactive-accent-hover);
}

/* Settings page improvements */
.setting-item-description {
    color: var(--text-muted);
    font-size: 0.9em;
    line-height: 1.4;
}

.sync-status {
    margin: 20px 0;
    padding: 15px;
    background-color: var(--background-secondary);
    border-radius: 8px;
    border-left: 4px solid var(--interactive-accent);
}

.sync-status h3 {
    margin: 0 0 10px 0;
    color: var(--text-normal);
}

.sync-status p {
    margin: 0;
    color: var(--text-muted);
    font-family: var(--font-monospace);
    font-size: 0.9em;
}

/* Worklog entry formatting in notes */
.worklog-entry {
    margin: 5px 0;
    padding-left: 10px;
    border-left: 2px solid var(--interactive-accent);
}

.worklog-task-link {
    font-weight: 600;
    color: var(--text-accent);
}

.worklog-summary {
    color: var(--text-muted);
    font-style: italic;
    margin-left: 15px;
}
