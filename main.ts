import { App, Plugin, PluginSettingTab, Setting, Notice, TFile, Modal } from 'obsidian';

interface WorklogSynchronizerSettings {
	syncTime: string;
	autoSync: boolean;
	dailyNotesPath: string;
	taskNotesPath: string;
	summaryFormat: string;
	showPreview: boolean;
}

const DEFAULT_SETTINGS: WorklogSynchronizerSettings = {
	syncTime: '18:00',
	autoSync: true,
	dailyNotesPath: '',
	taskNotesPath: '',
	summaryFormat: '- [b] [[{taskNote}]]\n     - {summary}',
	showPreview: false
};

interface WorklogEntry {
	date: string;
	content: string;
	taskNote: string;
}

interface SyncResult {
	success: boolean;
	entriesProcessed: number;
	dailyNotesUpdated: number;
	errors: string[];
	details: SyncDetail[];
}

interface SyncDetail {
	taskNote: string;
	date: string;
	action: 'created' | 'updated' | 'skipped' | 'error';
	message: string;
}

export default class WorklogSynchronizerPlugin extends Plugin {
	settings: WorklogSynchronizerSettings;
	syncInterval: number | null = null;

	async onload() {
		await this.loadSettings();

		// Add ribbon icon
		this.addRibbonIcon('sync', 'Sync Worklog', (evt: MouseEvent) => {
			if (this.settings.showPreview) {
				this.showSyncPreview();
			} else {
				this.syncWorklog();
			}
		});

		// Add command for manual sync
		this.addCommand({
			id: 'sync-worklog',
			name: 'Sync worklog entries',
			callback: () => {
				this.syncWorklog();
			}
		});

		// Add command for sync preview
		this.addCommand({
			id: 'preview-sync',
			name: 'Preview worklog sync',
			callback: () => {
				this.showSyncPreview();
			}
		});

		// Add settings tab
		this.addSettingTab(new WorklogSynchronizerSettingTab(this.app, this));

		// Setup automatic sync if enabled
		if (this.settings.autoSync) {
			this.setupAutoSync();
		}
	}

	onunload() {
		if (this.syncInterval) {
			window.clearInterval(this.syncInterval);
		}
	}

	async loadSettings() {
		this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
	}

	async saveSettings() {
		await this.saveData(this.settings);

		// Restart auto sync if settings changed
		if (this.syncInterval) {
			window.clearInterval(this.syncInterval);
		}
		if (this.settings.autoSync) {
			this.setupAutoSync();
		}
	}

	setupAutoSync() {
		const [hours, minutes] = this.settings.syncTime.split(':').map(Number);
		const now = new Date();
		const syncTime = new Date();
		syncTime.setHours(hours, minutes, 0, 0);

		// If sync time has passed today, schedule for tomorrow
		if (syncTime <= now) {
			syncTime.setDate(syncTime.getDate() + 1);
		}

		const timeUntilSync = syncTime.getTime() - now.getTime();

		// Set timeout for first sync, then interval for daily sync
		setTimeout(() => {
			this.syncWorklog();
			this.syncInterval = window.setInterval(() => {
				this.syncWorklog();
			}, 24 * 60 * 60 * 1000); // 24 hours
		}, timeUntilSync);
	}

	async syncWorklog() {
		try {
			new Notice('Starting worklog synchronization...');

			const taskNotes = await this.getTaskNotes();
			const worklogEntries = await this.extractWorklogEntries(taskNotes);

			let updatedCount = 0;
			for (const entry of worklogEntries) {
				const updated = await this.updateDailyNote(entry);
				if (updated) updatedCount++;
			}

			new Notice(`✅ Synchronized ${worklogEntries.length} worklog entries, updated ${updatedCount} daily notes`);
		} catch (error) {
			new Notice(`Error during sync: ${error.message}`);
			console.error('Worklog sync error:', error);
		}
	}

	async showSyncPreview() {
		try {
			new Notice('Generating sync preview...');

			const taskNotes = await this.getTaskNotes();
			const worklogEntries = await this.extractWorklogEntries(taskNotes);

			const result: SyncResult = {
				success: true,
				entriesProcessed: worklogEntries.length,
				dailyNotesUpdated: worklogEntries.length,
				errors: [],
				details: worklogEntries.map(entry => ({
					taskNote: entry.taskNote,
					date: entry.date,
					action: 'updated' as const,
					message: `Would add summary to daily note`
				}))
			};

			new SyncPreviewModal(this.app, result, () => this.syncWorklog()).open();
		} catch (error) {
			new Notice(`Error generating preview: ${error.message}`);
			console.error('Sync preview error:', error);
		}
	}

	async getTaskNotes(): Promise<TFile[]> {
		const files = this.app.vault.getMarkdownFiles();

		if (this.settings.taskNotesPath) {
			return files.filter(file =>
				file.path.startsWith(this.settings.taskNotesPath) &&
				!this.isDailyNote(file)
			);
		}

		// If no specific path, filter out daily notes
		return files.filter(file => !this.isDailyNote(file));
	}

	isDailyNote(file: TFile): boolean {
		if (this.settings.dailyNotesPath && !file.path.startsWith(this.settings.dailyNotesPath)) {
			return false;
		}

		// Check if filename matches date format
		const dateRegex = /\d{4}-\d{2}-\d{2}/;
		return dateRegex.test(file.basename);
	}

	async extractWorklogEntries(taskNotes: TFile[]): Promise<WorklogEntry[]> {
		const entries: WorklogEntry[] = [];

		for (const file of taskNotes) {
			const content = await this.app.vault.read(file);
			const worklogEntries = this.parseWorklogFromContent(content, file.basename);
			entries.push(...worklogEntries);
		}

		return entries;
	}

	parseWorklogFromContent(content: string, taskNoteName: string): WorklogEntry[] {
		const entries: WorklogEntry[] = [];
		const lines = content.split('\n');

		let currentDate = '';
		let currentEntries: string[] = [];

		for (const line of lines) {
			// Look for date headers (e.g., "## 2024-01-15" or "### 2024-01-15")
			const dateMatch = line.match(/^#{1,6}\s*(\d{4}-\d{2}-\d{2})/);
			if (dateMatch) {
				// Save previous date's entries
				if (currentDate && currentEntries.length > 0) {
					entries.push({
						date: currentDate,
						content: currentEntries.join('\n'),
						taskNote: taskNoteName
					});
				}

				currentDate = dateMatch[1];
				currentEntries = [];
			} else if (currentDate && line.trim()) {
				// Collect content under the current date
				if (line.startsWith('- ') || line.startsWith('* ') || line.startsWith('+ ')) {
					currentEntries.push(line.trim());
				}
			}
		}

		// Don't forget the last date's entries
		if (currentDate && currentEntries.length > 0) {
			entries.push({
				date: currentDate,
				content: currentEntries.join('\n'),
				taskNote: taskNoteName
			});
		}

		return entries;
	}

	async updateDailyNote(entry: WorklogEntry): Promise<boolean> {
		const dailyNotePath = this.getDailyNotePath(entry.date);
		let dailyNote = this.app.vault.getAbstractFileByPath(dailyNotePath);

		if (!dailyNote) {
			// Create daily note with template structure if it doesn't exist
			const templateContent = this.getDailyNoteTemplate(entry.date);
			await this.app.vault.create(dailyNotePath, templateContent);
			dailyNote = this.app.vault.getAbstractFileByPath(dailyNotePath);
		}

		if (dailyNote instanceof TFile) {
			const content = await this.app.vault.read(dailyNote);
			const summary = this.generateSummary(entry.content);
			const formattedEntry = this.settings.summaryFormat
				.replace('{taskNote}', entry.taskNote)
				.replace('{summary}', summary);

			// Check if this entry already exists to avoid duplicates
			if (!content.includes(formattedEntry)) {
				const updatedContent = this.insertIntoTasksSection(content, formattedEntry);
				await this.app.vault.modify(dailyNote, updatedContent);
				return true;
			}
		}
		return false;
	}

	getDailyNotePath(date: string): string {
		const basePath = this.settings.dailyNotesPath || '';
		return basePath ? `${basePath}/${date}.md` : `${date}.md`;
	}

	generateSummary(content: string): string {
		// Simple summary generation
		const lines = content.split('\n').filter(line => line.trim());

		if (lines.length === 1) {
			return lines[0].replace(/^[-*+]\s*/, '');
		}

		// For multiple lines, create a brief summary
		const firstLine = lines[0].replace(/^[-*+]\s*/, '');
		if (lines.length <= 3) {
			return lines.map(line => line.replace(/^[-*+]\s*/, '')).join('; ');
		}

		return `${firstLine} (and ${lines.length - 1} more items)`;
	}

	getDailyNoteTemplate(date: string): string {
		return `# ${date}

## Written Standup

Yesterday:
-

Today:
-

---
## 📌 Tasks



---
## 🗓️ Meetings



---
## 📝 Notes




`;
	}

	insertIntoTasksSection(content: string, formattedEntry: string): string {
		const lines = content.split('\n');
		let tasksIndex = -1;
		let nextSectionIndex = -1;

		// Find the Tasks section
		for (let i = 0; i < lines.length; i++) {
			if (lines[i].includes('📌 Tasks')) {
				tasksIndex = i;
				break;
			}
		}

		// If no Tasks section found, check if it's an old format daily note
		if (tasksIndex === -1) {
			// For old format notes (just date header), add after the header
			for (let i = 0; i < lines.length; i++) {
				if (lines[i].startsWith('# ')) {
					// Find the end of the header section (after any empty lines)
					let insertIndex = i + 1;
					while (insertIndex < lines.length && lines[insertIndex].trim() === '') {
						insertIndex++;
					}
					lines.splice(insertIndex, 0, formattedEntry);
					return lines.join('\n');
				}
			}
			// If no header found, just append
			return content + '\n' + formattedEntry;
		}

		// Find the next section after Tasks
		for (let i = tasksIndex + 1; i < lines.length; i++) {
			if (lines[i].startsWith('---') || lines[i].startsWith('## ')) {
				nextSectionIndex = i;
				break;
			}
		}

		// Insert the entry in the Tasks section
		if (nextSectionIndex === -1) {
			// No next section found, add at the end
			lines.push(formattedEntry);
		} else {
			// Insert before the next section, but after any existing content
			let insertIndex = nextSectionIndex;
			// Skip back over any empty lines before the next section
			while (insertIndex > tasksIndex && lines[insertIndex - 1].trim() === '') {
				insertIndex--;
			}
			lines.splice(insertIndex, 0, formattedEntry);
		}

		return lines.join('\n');
	}


}

class SyncPreviewModal extends Modal {
	result: SyncResult;
	onConfirm: () => void;

	constructor(app: App, result: SyncResult, onConfirm: () => void) {
		super(app);
		this.result = result;
		this.onConfirm = onConfirm;
	}

	onOpen() {
		const { contentEl } = this;
		contentEl.empty();

		contentEl.createEl('h2', { text: 'Worklog Sync Preview' });

		// Summary
		const summaryEl = contentEl.createDiv('sync-preview-summary');
		summaryEl.createEl('p', {
			text: `Found ${this.result.entriesProcessed} worklog entries to process`
		});
		summaryEl.createEl('p', {
			text: `${this.result.dailyNotesUpdated} daily notes will be updated`
		});

		if (this.result.errors.length > 0) {
			const errorsEl = summaryEl.createDiv('sync-preview-errors');
			errorsEl.createEl('h3', { text: 'Errors:' });
			this.result.errors.forEach(error => {
				errorsEl.createEl('p', { text: `❌ ${error}`, cls: 'error-text' });
			});
		}

		// Details
		if (this.result.details.length > 0) {
			const detailsEl = contentEl.createDiv('sync-preview-details');
			detailsEl.createEl('h3', { text: 'Details:' });

			this.result.details.forEach(detail => {
				const detailEl = detailsEl.createDiv('sync-detail');
				const icon = detail.action === 'created' ? '📝' :
							detail.action === 'updated' ? '✏️' :
							detail.action === 'error' ? '❌' : '⏭️';
				detailEl.createEl('p', {
					text: `${icon} ${detail.date} - ${detail.taskNote}: ${detail.message}`
				});
			});
		}

		// Buttons
		const buttonEl = contentEl.createDiv('sync-preview-buttons');

		const cancelBtn = buttonEl.createEl('button', { text: 'Cancel' });
		cancelBtn.onclick = () => this.close();

		if (this.result.success && this.result.entriesProcessed > 0) {
			const confirmBtn = buttonEl.createEl('button', {
				text: 'Proceed with Sync',
				cls: 'mod-cta'
			});
			confirmBtn.onclick = () => {
				this.close();
				this.onConfirm();
			};
		}
	}

	onClose() {
		const { contentEl } = this;
		contentEl.empty();
	}
}

class WorklogSynchronizerSettingTab extends PluginSettingTab {
	plugin: WorklogSynchronizerPlugin;

	constructor(app: App, plugin: WorklogSynchronizerPlugin) {
		super(app, plugin);
		this.plugin = plugin;
	}

	display(): void {
		const { containerEl } = this;
		containerEl.empty();

		containerEl.createEl('h2', { text: 'Worklog Synchronizer Settings' });

		new Setting(containerEl)
			.setName('Show preview before sync')
			.setDesc('Show a preview of changes before performing synchronization')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.showPreview)
				.onChange(async (value) => {
					this.plugin.settings.showPreview = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Auto sync')
			.setDesc('Automatically sync worklog entries daily')
			.addToggle(toggle => toggle
				.setValue(this.plugin.settings.autoSync)
				.onChange(async (value) => {
					this.plugin.settings.autoSync = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Sync time')
			.setDesc('Time to automatically sync (24-hour format, e.g., 18:00)')
			.addText(text => text
				.setPlaceholder('18:00')
				.setValue(this.plugin.settings.syncTime)
				.onChange(async (value) => {
					if (/^\d{1,2}:\d{2}$/.test(value)) {
						this.plugin.settings.syncTime = value;
						await this.plugin.saveSettings();
					} else {
						new Notice('Invalid time format. Use HH:MM (e.g., 18:00)');
					}
				}));

		// Add manual sync buttons
		new Setting(containerEl)
			.setName('Manual sync')
			.setDesc('Trigger synchronization now')
			.addButton(button => button
				.setButtonText('Sync Now')
				.setCta()
				.onClick(async () => {
					await this.plugin.syncWorklog();
				}))
			.addButton(button => button
				.setButtonText('Preview Sync')
				.onClick(async () => {
					await this.plugin.showSyncPreview();
				}));

		new Setting(containerEl)
			.setName('Daily notes path')
			.setDesc('Path to daily notes folder (leave empty for vault root)')
			.addText(text => text
				.setPlaceholder('Daily Notes')
				.setValue(this.plugin.settings.dailyNotesPath)
				.onChange(async (value) => {
					this.plugin.settings.dailyNotesPath = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Task notes path')
			.setDesc('Path to task notes folder (leave empty to scan all notes)')
			.addText(text => text
				.setPlaceholder('Tasks')
				.setValue(this.plugin.settings.taskNotesPath)
				.onChange(async (value) => {
					this.plugin.settings.taskNotesPath = value;
					await this.plugin.saveSettings();
				}));

		new Setting(containerEl)
			.setName('Summary format')
			.setDesc('Format for summary entries in daily notes. Use {taskNote} and {summary} as placeholders.')
			.addTextArea(text => text
				.setPlaceholder('- [b] [[{taskNote}]]\n     - {summary}')
				.setValue(this.plugin.settings.summaryFormat)
				.onChange(async (value) => {
					this.plugin.settings.summaryFormat = value;
					await this.plugin.saveSettings();
				}));
	}
}
