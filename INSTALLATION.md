# Installation Guide - Worklog Synchronizer

This guide will help you install and set up the Worklog Synchronizer plugin for Obsidian.

## Prerequisites

- Obsidian (version 0.15.0 or later)
- Basic understanding of Obsidian notes and folders

## Installation Methods

### Method 1: Manual Installation (Recommended for Development)

1. **Download or Clone**
   ```bash
   git clone https://github.com/your-username/obsidian-worklog-synchronizer.git
   cd obsidian-worklog-synchronizer
   ```

2. **Build the Plugin**
   ```bash
   # Install dependencies
   npm install
   
   # Build the plugin
   npm run build
   ```

3. **Copy to Obsidian**
   - Navigate to your Obsidian vault folder
   - Go to `.obsidian/plugins/` directory
   - Create a new folder named `obsidian-worklog-synchronizer`
   - Copy these files into the folder:
     - `main.js`
     - `manifest.json`
     - `styles.css`

4. **Enable the Plugin**
   - Restart Obsidian
   - Go to Settings → Community Plugins
   - Find "Worklog Synchronizer" and enable it

### Method 2: Direct File Installation

If you have the pre-built files:

1. **Create Plugin Directory**
   - Navigate to your vault's `.obsidian/plugins/` folder
   - Create a new folder: `obsidian-worklog-synchronizer`

2. **Copy Files**
   - Place `main.js`, `manifest.json`, and `styles.css` in the folder

3. **Enable Plugin**
   - Restart Obsidian
   - Enable in Settings → Community Plugins

## Initial Configuration

### 1. Access Settings
- Go to Settings → Community Plugins → Worklog Synchronizer

### 2. Basic Setup
- **Show preview before sync**: Enable if you want to see changes before applying
- **Auto sync**: Enable for automatic daily synchronization
- **Sync time**: Set your preferred time (e.g., 18:00 for 6 PM)

### 3. Path Configuration
- **Daily notes path**: Set if your daily notes are in a specific folder (e.g., "Daily Notes")
- **Task notes path**: Set if your task notes are in a specific folder (e.g., "Projects" or "Tasks")

### 4. Format Customization
- **Summary format**: Customize how entries appear in daily notes
- Default: `- [b] [[{taskNote}]]\n     - {summary}`

## Folder Structure Examples

### Option 1: Separate Folders
```
Your Vault/
├── Daily Notes/
│   ├── 2024-01-15.md
│   ├── 2024-01-16.md
│   └── ...
├── Projects/
│   ├── Project Alpha.md
│   ├── Project Beta.md
│   └── ...
└── ...
```

### Option 2: Root Level
```
Your Vault/
├── 2024-01-15.md (daily note)
├── 2024-01-16.md (daily note)
├── Project Alpha.md (task note)
├── Project Beta.md (task note)
└── ...
```

## Setting Up Your First Task Note

1. **Create a Task Note**
   ```markdown
   # My First Project
   
   ## 2024-01-15
   - Completed initial research
   - Set up project structure
   - Created documentation outline
   
   ## 2024-01-16
   - Implemented core functionality
   - Added unit tests
   - Reviewed code with team
   ```

2. **Test the Sync**
   - Use the ribbon icon or command palette
   - Run "Preview worklog sync" to see what will happen
   - Run "Sync worklog entries" to perform the sync

## Verification

After setup, verify everything works:

1. **Check Plugin Status**
   - Look for the sync icon in the ribbon
   - Check Settings → Community Plugins shows the plugin as enabled

2. **Test Manual Sync**
   - Create a simple task note with dated entries
   - Run a preview sync to see the results
   - Perform actual sync and check your daily note

3. **Verify Automatic Sync**
   - Check the sync status in plugin settings
   - Ensure the schedule shows correctly

## Troubleshooting

### Plugin Not Appearing
- Ensure all three files (main.js, manifest.json, styles.css) are in the correct folder
- Restart Obsidian completely
- Check the console (Ctrl+Shift+I) for error messages

### No Entries Found
- Verify your task notes use supported date formats (## 2024-01-15)
- Check that your task notes are in the configured path
- Ensure entries are formatted as bullet points or lists

### Sync Not Working
- Verify file paths in settings are correct
- Check that daily notes folder exists or can be created
- Look for error messages in the console

### Time Format Issues
- Use 24-hour format (18:00, not 6:00 PM)
- Ensure format is HH:MM (e.g., 09:30, not 9:30)

## Getting Help

If you encounter issues:

1. Check the console for error messages (Ctrl+Shift+I)
2. Verify your note formats match the examples
3. Try the preview mode to see what the plugin detects
4. Create an issue on the GitHub repository with:
   - Your Obsidian version
   - Plugin settings
   - Example notes that aren't working
   - Any error messages

## Next Steps

Once installed and configured:

1. **Organize Your Notes**: Set up a consistent structure for task and daily notes
2. **Customize Format**: Adjust the summary format to match your preferences
3. **Set Schedule**: Configure automatic sync for your workflow
4. **Create Templates**: Consider creating note templates for consistency

Happy note-taking! 🎉
