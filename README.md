# Worklog Synchronizer for Obsidian

A powerful Obsidian plugin that automatically synchronizes worklog entries between your daily notes and task notes, helping you maintain organized work logs across multiple notes.

## Features

- **Automatic Synchronization**: Sync worklog entries from task notes to daily notes
- **Smart Summary Generation**: Automatically creates concise summaries of your work entries
- **Daily Note Template Integration**: Automatically creates daily notes with proper structure and adds entries to the Tasks section
- **Flexible Scheduling**: Configure automatic sync at your preferred time (e.g., 6 PM daily)
- **Preview Mode**: See what will be synchronized before making changes
- **Customizable Formats**: Configure how summaries appear in your daily notes
- **Duplicate Prevention**: Avoids creating duplicate entries in your daily notes

## Problem Solved

This plugin addresses a common pain point for knowledge workers who use Obsidian:

> "I like to have daily notes to keep logs of what I did each day. But sometimes there are tickets/tasks that span multiple days (sometimes not consecutive), and I want to have a task note containing a worklog divided by dates where I can see everything I did for a specific task in one place. Currently, there's no way to synchronize notes on both sides without manual work, and I don't want the information to be exactly the same - I want detailed information in task notes and summaries in daily notes."

## How It Works

1. **Task Notes**: Create notes for your tasks/tickets with date-organized worklog entries
2. **Daily Notes**: The plugin automatically creates daily notes with a structured template (if they don't exist) and adds summarized entries to the "📌 Tasks" section
3. **Synchronization**: Run manually or automatically at your preferred time
4. **Template Structure**: New daily notes include sections for Written Standup, Tasks, Meetings, and Notes

### Example

**Task Note** (`PROJ-123 - User Authentication System.md`):
```markdown
---
created: 2024-01-10 09:30
tags:
  - authentication
  - security
ticket: https://company.atlassian.net/browse/PROJ-123
priority: 1
status:
  - In Progress
deadline: 2024-02-15
---
# 🚨 - Problem

- The current application lacks a secure user authentication system

# 📝 - Description

This project involves implementing a comprehensive user authentication system...

# 🎯 - Goals

- Implement secure user authentication using JWT tokens
- Add password reset functionality via email

---
### Work log

#### 2024-01-15
- Implemented user authentication system using JWT tokens
- Fixed login validation bug
- Updated API documentation
- Reviewed pull request #123

#### 2024-01-16
- Deployed authentication to staging
- Conducted user testing session
- Fixed styling issues in login form

---
### Resources

- [JWT Best Practices](https://auth0.com/blog/...)
```

**Daily Note** (`2024-01-15.md`) after sync:
```markdown
# 2024-01-15

## Written Standup

Yesterday:
-

Today:
-

---
## 📌 Tasks

- [b] [[Project Alpha]]
     - Implemented user authentication system (and 3 more items)

---
## 🗓️ Meetings



---
## 📝 Notes



```

## Installation

### Manual Installation

1. Download the latest release files (`main.js`, `manifest.json`, `styles.css`)
2. Create a folder named `obsidian-worklog-synchronizer` in your vault's `.obsidian/plugins/` directory
3. Copy the downloaded files into this folder
4. Restart Obsidian
5. Enable the plugin in Settings → Community Plugins

### Development Installation

1. Clone this repository: `git clone https://github.com/99Lys/obsidian-worklog-synchronizer.git`
2. Navigate to the plugin directory: `cd obsidian-worklog-synchronizer`
3. Install dependencies: `npm install`
4. Build the plugin: `npm run build`
5. Copy the built files (`main.js`, `manifest.json`, `styles.css`) to your vault's `.obsidian/plugins/obsidian-worklog-synchronizer/` directory
6. Restart Obsidian and enable the plugin

### Quick Build

Use the provided build scripts:
- **Unix/Mac/Linux**: `./build.sh`
- **Windows**: `build.bat`

## Configuration

### Settings

Access plugin settings via Settings → Community Plugins → Worklog Synchronizer

- **Show preview before sync**: Enable to see changes before they're applied
- **Auto sync**: Enable automatic daily synchronization
- **Sync time**: Set the time for automatic sync (24-hour format, e.g., 18:00)
- **Daily notes path**: Specify the folder containing your daily notes (optional)
- **Task notes path**: Specify the folder containing your task notes (optional)
- **Summary format**: Customize how entries appear in daily notes

### Summary Format

The summary format supports these placeholders:
- `{taskNote}`: Name of the task note
- `{summary}`: Generated summary of the worklog content
- `{date}`: Date of the worklog entry

Default format:
```
- [b] [[{taskNote}]]
     - {summary}
```

## Usage

### Manual Sync

- **Ribbon Icon**: Click the sync icon in the ribbon
- **Command Palette**: Use "Sync worklog entries" command
- **Settings**: Use the "Sync Now" button in plugin settings

### Preview Mode

- **Command Palette**: Use "Preview worklog sync" command
- **Settings**: Use the "Preview Sync" button in plugin settings

### Automatic Sync

1. Enable "Auto sync" in settings
2. Set your preferred sync time
3. The plugin will automatically sync daily at the specified time

## Task Note Format

Your task notes should follow this structure with YAML frontmatter and organized worklog entries:

```markdown
---
created: 2024-01-10 09:30
tags:
  - tag1
  - tag2
ticket: https://company.atlassian.net/browse/TICKET-123
priority: 1
status:
  - In Progress
deadline: 2024-02-15
---
# 🚨 - Problem

- Description of the problem

# 📝 - Description

- Detailed description of the task

# 🎯 - Goals

- List of goals to achieve

---
### Work log

#### 2024-01-15
- First work item
- Second work item
- Third work item

#### 2024-01-16
- More work items
- Additional tasks

#### 2024-01-17
- Also supports different header levels
- Flexible date formats supported

---
### Resources

- Links and references
```

### Supported Date Formats

- `## 2024-01-15` (ISO format)
- `### 2024/01/15` (Slash format)
- `#### 15-01-2024` (European format)
- `**2024-01-15**` (Bold format)
- `2024-01-15:` (Colon format)
- `Date: 2024-01-15` (Label format)

## Commands

- **Sync worklog entries**: Perform immediate synchronization
- **Preview worklog sync**: Show what would be synchronized
- **Show sync status**: Display current sync schedule and status

## Troubleshooting

### Common Issues

1. **No entries found**: Ensure your task notes use supported date header formats
2. **Sync not working**: Check that file paths in settings are correct
3. **Duplicates created**: The plugin should prevent duplicates, but manual edits might cause issues
4. **Time format errors**: Use 24-hour format (HH:MM) for sync time

### Debug Information

Check the developer console (Ctrl+Shift+I) for detailed error messages and sync logs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## License

MIT License - see LICENSE file for details.

## Support

If you find this plugin helpful, consider:
- ⭐ Starring the repository
- 🐛 Reporting bugs or suggesting features
- 📖 Improving documentation
- 💝 Sharing with other Obsidian users

---

**Note**: This plugin modifies your notes. Always backup your vault before using any new plugin.
